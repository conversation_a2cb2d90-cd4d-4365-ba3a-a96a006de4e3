'use client';

import { useState, useEffect } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase, FaEye } from 'react-icons/fa';
import dynamic from 'next/dynamic';
// Dynamically import the skills component
const Interactive3DSkills = dynamic(() => import('./Interactive3DSkills'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500"></div>
    </div>
  )
});

// Skills display is now ready immediately - no preloading needed

export default function AboutSection() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // AI/ML skills data for the new skills display
  const aimlSkills = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-xl text-blue-400" />,
      category: 'Programming',
      color: '#6366f1',
      description: 'Advanced Python programming for AI/ML development and data science'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-xl text-orange-400" />,
      category: 'Deep Learning',
      color: '#8b5cf6',
      description: 'Building and deploying neural networks with TensorFlow ecosystem'
    },
    {
      name: 'PyTorch',
      level: 88,
      icon: <FaBrain className="text-xl text-red-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Research and production-ready deep learning models with PyTorch'
    },
    {
      name: 'Scikit-Learn',
      level: 85,
      icon: <FaChartLine className="text-xl text-green-400" />,
      category: 'Machine Learning',
      color: '#8b5cf6',
      description: 'Classical machine learning algorithms and model evaluation'
    },
    {
      name: 'Computer Vision',
      level: 82,
      icon: <FaEye className="text-xl text-purple-400" />,
      category: 'AI Specialization',
      color: '#6366f1',
      description: 'Image processing, object detection, and visual recognition systems'
    },
    {
      name: 'Data Science',
      level: 85,
      icon: <FaDatabase className="text-xl text-yellow-400" />,
      category: 'Analytics',
      color: '#8b5cf6',
      description: 'Data analysis, visualization, and statistical modeling'
    }
  ];

  if (!isMounted) {
    return (
      <section id="about" className="py-20 relative min-h-screen">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white font-mono tracking-wider drop-shadow-lg">
              NEURAL INTERFACE
            </h2>
            <p className="text-lg text-white mt-4 font-semibold font-mono tracking-wide">AI/ML Engineer • Skills Matrix Loading...</p>
          </div>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="about" className="py-20 relative min-h-screen">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl mb-4 pearl-white-heading">
            About Me
          </h2>
          <p className="text-lg text-gray-200 max-w-3xl mx-auto">
            AI/ML Engineer passionate about building intelligent systems and solving complex problems through machine learning and data science.
          </p>
        </div>

        {/* Main content with left-right layout */}
        <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Image */}
          <div className="flex justify-center lg:justify-start order-2 lg:order-1">
            <div className="relative w-full max-w-sm lg:max-w-md">
              {/* Image container with glassmorphism effect */}
              <div className="relative overflow-hidden rounded-2xl glass border-2 border-white/20 shadow-2xl">
                <img
                  src="/image2.jpg"
                  alt="Jeetheshwar Aalam - About"
                  className="w-full h-auto object-cover transition-transform duration-300 hover:scale-105"
                  onError={(e) => {
                    // Fallback if image2 doesn't exist
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                {/* Fallback placeholder */}
                <div className="hidden w-full h-96 bg-gradient-to-br from-blue-500/20 to-purple-500/20 items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 mx-auto mb-4 bg-white/10 rounded-full flex items-center justify-center">
                      <svg className="w-12 h-12 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-white/70 text-sm">Add image2.jpg to public folder</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-xl"></div>
            </div>
          </div>

          {/* Right side - Skills Display */}
          <div className="order-1 lg:order-2">
            <Interactive3DSkills
              skills={aimlSkills}
              className="w-full"
            />
          </div>
        </div>
      </div>
    </section>
  );
}