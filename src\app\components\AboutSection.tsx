'use client';

import { useState, useEffect } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase, FaEye } from 'react-icons/fa';
import dynamic from 'next/dynamic';
// Dynamically import the skills component
const Interactive3DSkills = dynamic(() => import('./Interactive3DSkills'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500"></div>
    </div>
  )
});

// Dynamically import the 3D model component
const Model3D = dynamic(() => import('./Model3D'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-purple-500"></div>
    </div>
  )
});

// Skills display is now ready immediately - no preloading needed

export default function AboutSection() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Preload the 3D model for better performance
    if (typeof window !== 'undefined') {
      import('./Model3D').then(({ preloadModel }) => {
        preloadModel('/a_windy_day.glb');
      });
    }
  }, []);

  // AI/ML skills data for the new skills display
  const aimlSkills = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-xl text-blue-400" />,
      category: 'Programming',
      color: '#6366f1',
      description: 'Advanced Python programming for AI/ML development and data science'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-xl text-orange-400" />,
      category: 'Deep Learning',
      color: '#8b5cf6',
      description: 'Building and deploying neural networks with TensorFlow ecosystem'
    },
    {
      name: 'PyTorch',
      level: 88,
      icon: <FaBrain className="text-xl text-red-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Research and production-ready deep learning models with PyTorch'
    },
    {
      name: 'Scikit-Learn',
      level: 85,
      icon: <FaChartLine className="text-xl text-green-400" />,
      category: 'Machine Learning',
      color: '#8b5cf6',
      description: 'Classical machine learning algorithms and model evaluation'
    },
    {
      name: 'Computer Vision',
      level: 82,
      icon: <FaEye className="text-xl text-purple-400" />,
      category: 'AI Specialization',
      color: '#6366f1',
      description: 'Image processing, object detection, and visual recognition systems'
    },
    {
      name: 'Data Science',
      level: 85,
      icon: <FaDatabase className="text-xl text-yellow-400" />,
      category: 'Analytics',
      color: '#8b5cf6',
      description: 'Data analysis, visualization, and statistical modeling'
    }
  ];

  if (!isMounted) {
    return (
      <section id="about" className="py-20 relative min-h-screen">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white font-mono tracking-wider drop-shadow-lg">
              NEURAL INTERFACE
            </h2>
            <p className="text-lg text-white mt-4 font-semibold font-mono tracking-wide">AI/ML Engineer • Skills Matrix Loading...</p>
          </div>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="about" className="py-20 relative min-h-screen">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl mb-4 pearl-white-heading">
            About Me
          </h2>
          <p className="text-lg text-gray-200 max-w-3xl mx-auto">
            AI/ML Engineer passionate about building intelligent systems and solving complex problems through machine learning and data science.
          </p>
        </div>

        {/* 3D Model and Skills Layout */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 3D Model Section */}
            <div className="order-2 lg:order-1">
              <div className="glass rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300">
                <h3 className="text-xl font-semibold text-white mb-4 text-center pearl-white-heading">
                  Interactive 3D Experience
                </h3>
                <div className="relative overflow-hidden rounded-xl">
                  <Model3D
                    modelPath="/a_windy_day.glb"
                    className="w-full h-80 md:h-96"
                    scale={1.2}
                    position={[0, -0.5, 0]}
                    enableControls={true}
                    autoRotate={true}
                  />
                  {/* Subtle gradient overlay for better integration */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent pointer-events-none"></div>
                </div>
                <div className="flex items-center justify-center mt-4 space-x-4 text-gray-300 text-sm">
                  <span className="flex items-center">
                    <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                    Drag to rotate
                  </span>
                  <span className="flex items-center">
                    <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                    Scroll to zoom
                  </span>
                </div>
              </div>
            </div>

            {/* Skills Section */}
            <div className="order-1 lg:order-2">
              <Interactive3DSkills
                skills={aimlSkills}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}