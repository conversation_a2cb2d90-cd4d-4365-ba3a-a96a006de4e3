'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Line, PieChart, Pie,
  ResponsiveContainer, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, Cell
} from 'recharts';

export default function DataVisualization() {
  const [activeChart, setActiveChart] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const element = document.getElementById('data-viz');
      if (element) {
        const top = element.getBoundingClientRect().top;
        const isVisible = top < window.innerHeight * 0.8;
        setIsVisible(isVisible);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMounted]);

  const modelAccuracy = [
    { epoch: 1, accuracy: 65, loss: 1.2 },
    { epoch: 2, accuracy: 72, loss: 0.9 },
    { epoch: 3, accuracy: 78, loss: 0.7 },
    { epoch: 4, accuracy: 82, loss: 0.6 },
    { epoch: 5, accuracy: 86, loss: 0.5 },
    { epoch: 6, accuracy: 89, loss: 0.4 },
    { epoch: 7, accuracy: 91, loss: 0.35 },
    { epoch: 8, accuracy: 92, loss: 0.3 },
    { epoch: 9, accuracy: 93, loss: 0.28 },
    { epoch: 10, accuracy: 94, loss: 0.25 },
  ];

  const confusionMatrix = [
    { class: 'Cat', TP: 120, FP: 10, FN: 5 },
    { class: 'Dog', TP: 95, FP: 8, FN: 12 },
    { class: 'Bird', TP: 80, FP: 15, FN: 8 },
    { class: 'Fish', TP: 105, FP: 5, FN: 10 },
  ];

  const classDistribution = [
    { name: 'Class A', value: 400 },
    { name: 'Class B', value: 300 },
    { name: 'Class C', value: 200 },
    { name: 'Class D', value: 100 },
  ];

  const COLORS = ['#6666ff', '#0044cc', '#ffc658', '#ff8042'];

  const charts = [
    {
      title: 'Model Accuracy Over Epochs',
      description: 'Tracking the improvement of model accuracy during training',
      chart: (
        <ResponsiveContainer width="100%" height={400}>
          <LineChart
            data={modelAccuracy}
            margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="epoch" label={{ value: 'Epoch', position: 'insideBottom', offset: -10 }} />
            <YAxis label={{ value: 'Accuracy (%)', angle: -90, position: 'insideLeft' }} domain={[60, 100]} />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="accuracy"
              stroke="#8884d8"
              activeDot={{ r: 8 }}
              strokeWidth={2}
              name="Accuracy"
              animationBegin={isVisible ? 0 : 1000}
              animationDuration={2000}
            />
            <Line
              type="monotone"
              dataKey="loss"
              stroke="#82ca9d"
              strokeWidth={2}
              name="Loss"
              animationBegin={isVisible ? 300 : 1000}
              animationDuration={2000}
            />
          </LineChart>
        </ResponsiveContainer>
      )
    },
    {
      title: 'Confusion Matrix',
      description: 'Performance breakdown by class for multi-class classification',
      chart: (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={confusionMatrix}
            margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="class" />
            <YAxis label={{ value: 'Count', angle: -90, position: 'insideLeft' }} />
            <Tooltip />
            <Legend />
            <Bar
              dataKey="TP"
              fill="#8884d8"
              name="True Positives"
              animationBegin={isVisible ? 0 : 1000}
              animationDuration={2000}
            />
            <Bar
              dataKey="FP"
              fill="#82ca9d"
              name="False Positives"
              animationBegin={isVisible ? 300 : 1000}
              animationDuration={2000}
            />
            <Bar
              dataKey="FN"
              fill="#ffc658"
              name="False Negatives"
              animationBegin={isVisible ? 600 : 1000}
              animationDuration={2000}
            />
          </BarChart>
        </ResponsiveContainer>
      )
    },
    {
      title: 'Class Distribution',
      description: 'Dataset class balance visualization',
      chart: (
        <ResponsiveContainer width="100%" height={400}>
          <PieChart>
            <Pie
              data={classDistribution}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={150}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              animationBegin={isVisible ? 0 : 1000}
              animationDuration={2000}
            >
              {classDistribution.map((_, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      )
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveChart(prev => (prev + 1) % charts.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [charts.length]);

  return (
    <section id="data-viz" className="py-20 relative">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold glossy-black-text">
            Data Visualizations
          </h2>
        </div>

        <div className="glass rounded-xl shadow-xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-oxford-blue dark:text-white mb-2">
              {charts[activeChart].title}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              {charts[activeChart].description}
            </p>
          </div>

          <div className="h-[500px]">
            {charts[activeChart].chart}
          </div>

          <div className="flex justify-center mt-6 gap-2">
            {charts.map((_, index) => (
              <button
                key={index}
                onClick={() => setActiveChart(index)}
                className={`w-4 h-4 rounded-full transition-all duration-300 ${
                  activeChart === index
                    ? 'bg-gradient-to-r from-purple-500 to-indigo-500 scale-125 shadow-lg'
                    : 'bg-white/30 dark:bg-black/30 hover:bg-white/50 dark:hover:bg-black/50 hover:scale-110'
                }`}
                aria-label={`Show chart ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}