'use client';

import { useEffect, useState } from 'react';

interface ColorStop {
  color: string;
  position: number;
}

export default function DynamicBackground() {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [currentGradient, setCurrentGradient] = useState('');
  const [isMounted, setIsMounted] = useState(false);

  // Define the color progression for different sections with smoother transitions
  const colorStops: ColorStop[] = [
    { color: '#e6e6fa', position: 0 },      // Lavender - Home section
    { color: '#b19cd9', position: 0.15 },   // Transition color
    { color: '#6666ff', position: 0.25 },   // Blue - About section
    { color: '#4d4dcc', position: 0.35 },   // Transition color
    { color: '#000000', position: 0.45 },   // Black - Projects section
    { color: '#333366', position: 0.55 },   // Transition color
    { color: '#6666ff', position: 0.65 },   // Blue - Data Viz section
    { color: '#4d4dcc', position: 0.75 },   // Transition color
    { color: '#0044cc', position: 0.85 },   // Blue - Testimonials section
    { color: '#003399', position: 0.95 },   // Transition color
    { color: '#000000', position: 1.0 },    // Black - Contact section
  ];

  // Function to interpolate between two colors
  const interpolateColor = (color1: string, color2: string, factor: number): string => {
    // Convert hex to RGB
    const hex2rgb = (hex: string) => {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return [r, g, b];
    };

    // Convert RGB to hex
    const rgb2hex = (r: number, g: number, b: number) => {
      return `#${Math.round(r).toString(16).padStart(2, '0')}${Math.round(g).toString(16).padStart(2, '0')}${Math.round(b).toString(16).padStart(2, '0')}`;
    };

    const [r1, g1, b1] = hex2rgb(color1);
    const [r2, g2, b2] = hex2rgb(color2);

    const r = r1 + (r2 - r1) * factor;
    const g = g1 + (g2 - g1) * factor;
    const b = b1 + (b2 - b1) * factor;

    return rgb2hex(r, g, b);
  };

  // Function to get current color based on scroll progress
  const getCurrentColor = (progress: number): string => {
    // Clamp progress between 0 and 1
    progress = Math.max(0, Math.min(1, progress));

    // Find the two color stops to interpolate between
    for (let i = 0; i < colorStops.length - 1; i++) {
      const currentStop = colorStops[i];
      const nextStop = colorStops[i + 1];

      if (progress >= currentStop.position && progress <= nextStop.position) {
        const segmentProgress = (progress - currentStop.position) / (nextStop.position - currentStop.position);
        return interpolateColor(currentStop.color, nextStop.color, segmentProgress);
      }
    }

    // If we're at the end, return the last color
    return colorStops[colorStops.length - 1].color;
  };

  // Function to create a smooth multi-stop gradient
  const createGradient = (progress: number): string => {
    const currentColor = getCurrentColor(progress);
    const midProgress = Math.min(1, progress + 0.15);
    const midColor = getCurrentColor(midProgress);
    const nextProgress = Math.min(1, progress + 0.3);
    const nextColor = getCurrentColor(nextProgress);

    return `linear-gradient(180deg, ${currentColor} 0%, ${midColor} 50%, ${nextColor} 100%)`;
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = scrollTop / docHeight;

      setScrollProgress(scrollPercent);
      setCurrentGradient(createGradient(scrollPercent));
    };

    // Initial call
    handleScroll();

    // Add scroll listener with throttling for performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isMounted]);

  return (
    <div
      className="fixed inset-0 -z-10 dynamic-bg-transition"
      style={{
        background: currentGradient || 'linear-gradient(180deg, #e6e6fa 0%, #6666ff 100%)',
      }}
    >

    </div>
  );
}
