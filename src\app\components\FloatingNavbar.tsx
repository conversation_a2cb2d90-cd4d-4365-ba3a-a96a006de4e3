'use client';

import { useState, useEffect } from 'react';
import { FaHome, FaUser, FaProjectDiagram, FaChartBar, FaComments, FaEnvelope } from 'react-icons/fa';

interface NavItem {
  id: string;
  label: string;
  icon: JSX.Element;
  href: string;
}

const navItems: NavItem[] = [
  { id: 'home', label: 'Home', icon: <FaHome />, href: '#home' },
  { id: 'about', label: 'About', icon: <FaUser />, href: '#about' },
  { id: 'projects', label: 'Projects', icon: <FaProjectDiagram />, href: '#projects' },
  { id: 'data-viz', label: 'Analytics', icon: <FaChartBar />, href: '#data-viz' },
  { id: 'testimonials', label: 'Reviews', icon: <FaComments />, href: '#testimonials' },
  { id: 'contact', label: 'Contact', icon: <FaEnvelope />, href: '#contact' },
];

export default function FloatingNavbar() {
  const [activeSection, setActiveSection] = useState('home');
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Hide/show navbar based on scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      setLastScrollY(currentScrollY);

      // Update active section based on scroll position
      const sections = navItems.map(item => item.id);
      const sectionElements = sections.map(id => document.getElementById(id));

      let current = 'home';
      sectionElements.forEach((element, index) => {
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            current = sections[index];
          }
        }
      });

      setActiveSection(current);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY, isMounted]);

  const scrollToSection = (href: string) => {
    if (!isMounted) return;

    const targetId = href.replace('#', '');
    const element = document.getElementById(targetId);

    if (element) {
      const offsetTop = targetId === 'home' ? 0 : element.offsetTop - 100; // No offset for home, 100px for others
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  return (
    <nav
      className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ease-out ${
        isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      }`}
    >
      {/* Glassmorphism Container */}
      <div className="bg-white/10 dark:bg-black/10 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-full px-3 md:px-6 py-2 md:py-3 shadow-2xl">
        <div className="flex items-center space-x-1 md:space-x-2">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => scrollToSection(item.href)}
              className={`relative group flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 ${
                activeSection === item.id
                  ? 'text-black shadow-lg scale-105 border'
                  : 'text-white/80 hover:bg-white/20 hover:text-white border border-transparent hover:border-white/30'
              }`}
              style={{
                backgroundColor: activeSection === item.id ? '#6666ff' : undefined,
                borderColor: activeSection === item.id ? '#6666ff' : undefined
              }}
              aria-label={`Navigate to ${item.label}`}
            >
              {/* Icon */}
              <span className={`text-sm md:text-lg transition-all duration-300 ${
                activeSection === item.id ? 'text-black' : ''
              }`}>
                {item.icon}
              </span>

              {/* Label - Hidden on mobile, shown on larger screens */}
              <span className={`hidden lg:block text-xs md:text-sm font-medium whitespace-nowrap ${
                activeSection === item.id ? 'text-black' : ''
              }`}>
                {item.label}
              </span>

              {/* Active indicator dot */}
              {activeSection === item.id && (
                <div className="absolute -top-1 -right-1 w-2 h-2 rounded-full animate-pulse shadow-lg" style={{ backgroundColor: '#6666ff', boxShadow: '0 0 10px rgba(102, 102, 255, 0.5)' }} />
              )}

              {/* Hover tooltip for mobile and tablet */}
              <div className="lg:hidden absolute -bottom-10 left-1/2 transform -translate-x-1/2 bg-black/90 text-white text-xs px-2 py-1 rounded-md opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-10">
                {item.label}
                <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-black/90 rotate-45"></div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Enhanced glow effect */}
      <div className="absolute inset-0 rounded-full blur-xl -z-10 animate-pulse" style={{ background: 'linear-gradient(to right, rgba(0, 204, 204, 0.1), rgba(0, 172, 230, 0.05), rgba(0, 204, 204, 0.1))' }} />

      {/* Additional subtle shadow */}
      <div className="absolute inset-0 bg-black/5 rounded-full blur-sm -z-20" />
    </nav>
  );
}
