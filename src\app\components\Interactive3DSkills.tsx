'use client';

import { useEffect, useState } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase, FaEye, FaNetworkWired } from 'react-icons/fa';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  category: string;
  color: string;
  description: string;
}

interface SkillMeterProps {
  skill: SkillData;
  index: number;
  isVisible: boolean;
}

function SkillMeter({ skill, index, isVisible }: SkillMeterProps) {
  const [animatedLevel, setAnimatedLevel] = useState(0);
  const [glowPosition, setGlowPosition] = useState('');

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        setAnimatedLevel(skill.level);
      }, index * 200); // Stagger animations

      return () => clearTimeout(timer);
    }
  }, [isVisible, skill.level, index]);

  // Generate random glow position for each skill card
  useEffect(() => {
    const positions = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
    const randomPosition = positions[Math.floor(Math.random() * positions.length)];
    setGlowPosition(randomPosition);
  }, []);

  // Determine hanging animation group (1,2; 1,2 pattern)
  const hangingGroup = (index % 4 < 2) ? 'group-1' : 'group-2';

  // Convert hex color to RGB for background
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 102, g: 102, b: 255 }; // fallback color
  };

  const rgb = hexToRgb(skill.color);
  const backgroundStyle = {
    background: `linear-gradient(135deg, rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.25) 0%, rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.15) 50%, rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1) 100%)`,
    borderColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.5)`,
    backdropFilter: 'blur(10px)'
  };

  const glowStyle = {
    backgroundColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.6)`,
    boxShadow: `0 0 30px rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.4), 0 0 60px rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`
  };

  return (
    <div className={`hanging-skill hanging-skill-${hangingGroup} relative rounded-xl p-5 group hover:scale-105 transition-all duration-300 shadow-lg border-2 overflow-hidden skill-card-content`}
         style={{...backgroundStyle, minHeight: '180px'}}>
      {/* Dynamic corner glow - outer */}
      <div
        className="absolute w-20 h-20 rounded-full"
        style={{
          ...glowStyle,
          filter: 'blur(15px)',
          animation: 'glow-pulse 3s ease-in-out infinite',
          ...(glowPosition === 'top-left' && { top: '-10px', left: '-10px' }),
          ...(glowPosition === 'top-right' && { top: '-10px', right: '-10px' }),
          ...(glowPosition === 'bottom-left' && { bottom: '-10px', left: '-10px' }),
          ...(glowPosition === 'bottom-right' && { bottom: '-10px', right: '-10px' })
        }}
      />

      {/* Additional inner glow for more intensity */}
      <div
        className="absolute w-12 h-12 rounded-full"
        style={{
          backgroundColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.9)`,
          filter: 'blur(8px)',
          animation: 'glow-pulse 2s ease-in-out infinite',
          animationDelay: '0.5s',
          ...(glowPosition === 'top-left' && { top: '5px', left: '5px' }),
          ...(glowPosition === 'top-right' && { top: '5px', right: '5px' }),
          ...(glowPosition === 'bottom-left' && { bottom: '5px', left: '5px' }),
          ...(glowPosition === 'bottom-right' && { bottom: '5px', right: '5px' })
        }}
      />

      <div className="flex flex-col space-y-3 relative z-10 h-full">
        {/* Header with icon and title */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2.5 flex-1 min-w-0">
            <div className="p-2 rounded-lg bg-white/15 backdrop-blur-sm border border-white/30 flex-shrink-0">
              {skill.icon}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium text-sm leading-tight truncate">{skill.name}</h3>
              <p className="text-gray-200 text-xs font-light mt-0.5 opacity-90">{skill.category}</p>
            </div>
          </div>
          <div className="text-right flex-shrink-0 ml-2">
            <span className="text-lg font-medium text-white">
              {animatedLevel}%
            </span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="relative h-1.5 bg-white/15 rounded-full overflow-hidden">
          <div
            className="absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out"
            style={{
              width: `${animatedLevel}%`,
              background: `linear-gradient(90deg, ${skill.color}, ${skill.color}dd)`
            }}
          />
        </div>

        {/* Skill Description */}
        <div className="flex-1 flex items-end">
          <p className="text-gray-200 text-xs font-light leading-relaxed opacity-0 group-hover:opacity-100 transition-opacity duration-300 line-clamp-2">
            {skill.description}
          </p>
        </div>
      </div>
    </div>
  );
}

interface Interactive3DSkillsProps {
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations when component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Default AI/ML skills if none provided
  const defaultSkills: SkillData[] = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-xl text-blue-400" />,
      category: 'Programming',
      color: '#6366f1',
      description: 'Advanced Python programming for AI/ML development and data science'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-xl text-orange-400" />,
      category: 'Deep Learning',
      color: '#8b5cf6',
      description: 'Building and deploying neural networks with TensorFlow ecosystem'
    },
    {
      name: 'PyTorch',
      level: 88,
      icon: <FaBrain className="text-xl text-red-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Research and production-ready deep learning models with PyTorch'
    },
    {
      name: 'Scikit-Learn',
      level: 85,
      icon: <FaChartLine className="text-xl text-green-400" />,
      category: 'Machine Learning',
      color: '#8b5cf6',
      description: 'Classical machine learning algorithms and model evaluation'
    },
    {
      name: 'Computer Vision',
      level: 82,
      icon: <FaEye className="text-xl text-purple-400" />,
      category: 'AI Specialization',
      color: '#6366f1',
      description: 'Image processing, object detection, and visual recognition systems'
    },
    {
      name: 'NLP',
      level: 80,
      icon: <FaCode className="text-xl text-indigo-400" />,
      category: 'AI Specialization',
      color: '#8b5cf6',
      description: 'Natural Language Processing and text analysis applications'
    },
    {
      name: 'Neural Networks',
      level: 87,
      icon: <FaNetworkWired className="text-xl text-cyan-400" />,
      category: 'Deep Learning',
      color: '#6366f1',
      description: 'Designing and optimizing neural network architectures'
    },
    {
      name: 'Data Science',
      level: 83,
      icon: <FaDatabase className="text-xl text-yellow-400" />,
      category: 'Analytics',
      color: '#8b5cf6',
      description: 'Data analysis, visualization, and statistical modeling'
    }
  ];

  const skillsToDisplay = skills.length > 0 ? skills : defaultSkills;

  return (
    <div className={`${className}`}>
      {/* Clean Professional Header */}
      <div className="text-center mb-12">
        <h3 className="text-2xl font-bold text-white mb-4">
          Technical Expertise
        </h3>
        <p className="text-gray-300 max-w-2xl mx-auto">
          Specialized skills in artificial intelligence, machine learning, and data science with hands-on experience in modern frameworks and technologies.
        </p>
      </div>

      {/* Skills Grid - Clean and Professional */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {skillsToDisplay.map((skill, index) => (
          <SkillMeter
            key={skill.name}
            skill={skill}
            index={index}
            isVisible={isVisible}
          />
        ))}
      </div>
    </div>
  );
}

// Export for compatibility (no longer needed but keeping for existing imports)
export function preloadInteractive3D(modelPath?: string) {
  console.log('3D model preloading no longer needed - using modern skills display');
}
