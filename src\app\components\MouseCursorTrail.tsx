'use client';

import { useEffect, useRef } from 'react';

const MouseCursorTrail = () => {
  const coordsRef = useRef({ x: 0, y: 0 });
  const circlesRef = useRef<HTMLDivElement[]>([]);
  const animationRef = useRef<number>();

  const colors = [
    "#ffb56b",
    "#fdaf69",
    "#f89d63",
    "#f59761",
    "#ef865e",
    "#ec805d",
    "#e36e5c",
    "#df685c",
    "#d5585c",
    "#d1525c",
    "#c5415d",
    "#c03b5d",
    "#b22c5e",
    "#ac265e",
    "#9c155f",
    "#950f5f",
    "#830060",
    "#7c0060",
    "#680060",
    "#60005f",
    "#48005f",
    "#3d005e"
  ];

  useEffect(() => {
    // Create circles and add them to the DOM
    const circles: HTMLDivElement[] = [];
    
    for (let i = 0; i < 20; i++) {
      const circle = document.createElement('div');
      circle.className = 'cursor-trail-circle';
      circle.style.cssText = `
        height: 24px;
        width: 24px;
        border-radius: 24px;
        background-color: ${colors[i % colors.length]};
        position: fixed;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 99999999;
        transform-origin: center;
      `;
      
      // Initialize circle properties
      (circle as any).x = 0;
      (circle as any).y = 0;
      
      document.body.appendChild(circle);
      circles.push(circle);
    }
    
    circlesRef.current = circles;

    // Mouse move event listener
    const handleMouseMove = (e: MouseEvent) => {
      coordsRef.current.x = e.clientX;
      coordsRef.current.y = e.clientY;
    };

    // Animation function
    const animateCircles = () => {
      let x = coordsRef.current.x;
      let y = coordsRef.current.y;
      
      circlesRef.current.forEach((circle, index) => {
        circle.style.left = x - 12 + "px";
        circle.style.top = y - 12 + "px";
        
        const scale = (circlesRef.current.length - index) / circlesRef.current.length;
        circle.style.transform = `scale(${scale})`;
        
        (circle as any).x = x;
        (circle as any).y = y;

        const nextCircle = circlesRef.current[index + 1] || circlesRef.current[0];
        x += ((nextCircle as any).x - x) * 0.3;
        y += ((nextCircle as any).y - y) * 0.3;
      });
     
      animationRef.current = requestAnimationFrame(animateCircles);
    };

    // Start animation and event listener
    window.addEventListener("mousemove", handleMouseMove);
    animateCircles();

    // Cleanup function
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      // Remove circles from DOM
      circles.forEach(circle => {
        if (circle.parentNode) {
          circle.parentNode.removeChild(circle);
        }
      });
    };
  }, []);

  // This component doesn't render anything visible itself
  return null;
};

export default MouseCursorTrail;
