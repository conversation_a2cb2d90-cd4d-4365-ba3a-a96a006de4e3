'use client';

import { Suspense, useRef, useState, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { useGLTF, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface MouseFollowingModelProps {
  modelPath: string;
  scale?: number;
  className?: string;
  followIntensity?: number;
  rotationSpeed?: number;
}

function OwlModel({
  modelPath,
  scale = 1,
  followIntensity = 0.1,
  rotationSpeed = 0.002
}: {
  modelPath: string;
  scale: number;
  followIntensity: number;
  rotationSpeed: number;
}) {
  let scene;
  try {
    const gltf = useGLTF(modelPath);
    scene = gltf.scene;
  } catch (error) {
    console.warn(`Could not load model: ${modelPath}. Using fallback.`);
    // Return a simple fallback mesh if model fails to load
    return (
      <mesh scale={scale}>
        <sphereGeometry args={[0.5, 16, 16]} />
        <meshStandardMaterial color="#6666ff" transparent opacity={0.3} />
      </mesh>
    );
  }
  const modelRef = useRef<THREE.Group>(null);
  const { viewport } = useThree();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      // Convert mouse position to normalized coordinates (-1 to 1)
      const x = (event.clientX / window.innerWidth) * 2 - 1;
      const y = -(event.clientY / window.innerHeight) * 2 + 1;
      setMousePosition({ x, y });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  useFrame((state) => {
    if (modelRef.current) {
      // Smooth rotation
      modelRef.current.rotation.y += rotationSpeed;

      // Calculate target position based on mouse
      const targetX = mousePosition.x * viewport.width * followIntensity;
      const targetY = mousePosition.y * viewport.height * followIntensity;

      // Smooth easing to target position
      modelRef.current.position.x = THREE.MathUtils.lerp(
        modelRef.current.position.x,
        targetX,
        0.05
      );
      modelRef.current.position.y = THREE.MathUtils.lerp(
        modelRef.current.position.y,
        targetY,
        0.05
      );

      // Optional: Make the owl look towards the mouse
      const lookAtX = mousePosition.x * 0.3;
      const lookAtY = mousePosition.y * 0.3;
      
      modelRef.current.rotation.x = THREE.MathUtils.lerp(
        modelRef.current.rotation.x,
        -lookAtY,
        0.03
      );
      modelRef.current.rotation.z = THREE.MathUtils.lerp(
        modelRef.current.rotation.z,
        lookAtX * 0.2,
        0.03
      );
    }
  });

  return (
    <group ref={modelRef} scale={scale}>
      <primitive object={scene.clone()} />
    </group>
  );
}

function LoadingSpinner() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white/30"></div>
    </div>
  );
}

export default function MouseFollowingModel({
  modelPath,
  scale = 1,
  className = "w-full h-full",
  followIntensity = 0.1,
  rotationSpeed = 0.002
}: MouseFollowingModelProps) {
  return (
    <div className={className}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Subtle lighting for background model */}
        <ambientLight intensity={0.2} />
        <directionalLight position={[5, 5, 5]} intensity={0.3} />
        <pointLight position={[-5, -5, -5]} intensity={0.2} />

        {/* Environment for subtle reflections */}
        <Environment preset="studio" />

        {/* Mouse-following Owl Model */}
        <Suspense fallback={
          <mesh>
            <sphereGeometry args={[0.5, 16, 16]} />
            <meshStandardMaterial color="#6666ff" transparent opacity={0.3} />
          </mesh>
        }>
          <OwlModel
            modelPath={modelPath}
            scale={scale}
            followIntensity={followIntensity}
            rotationSpeed={rotationSpeed}
          />
        </Suspense>
      </Canvas>
    </div>
  );
}

// Preload the model for better performance
export function preloadOwlModel(modelPath: string) {
  useGLTF.preload(modelPath);
}
