'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';

interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  mockupType: 'dashboard' | 'mobile' | 'web' | 'ai';
  technologies: string[];
}

const projects: Project[] = [
  {
    id: 1,
    title: "AI-Powered Analytics Dashboard",
    description: "Real-time data visualization platform with machine learning insights and predictive analytics for business intelligence.",
    category: "AI/ML",
    mockupType: "dashboard",
    technologies: ["Python", "TensorFlow", "React", "D3.js"]
  },
  {
    id: 2,
    title: "Computer Vision Mobile App",
    description: "Mobile application leveraging computer vision for object detection and image classification with real-time processing.",
    category: "Mobile",
    mockupType: "mobile",
    technologies: ["React Native", "OpenCV", "PyTorch", "Firebase"]
  },
  {
    id: 3,
    title: "NLP Recommendation Engine",
    description: "Advanced natural language processing system for personalized content recommendations using transformer models.",
    category: "NLP",
    mockupType: "web",
    technologies: ["BERT", "FastAPI", "PostgreSQL", "Docker"]
  },
  {
    id: 4,
    title: "Deep Learning Research Platform",
    description: "Collaborative research environment for deep learning experiments with automated model training and evaluation.",
    category: "Research",
    mockupType: "ai",
    technologies: ["PyTorch", "MLflow", "Kubernetes", "Jupyter"]
  },
  {
    id: 5,
    title: "Data Science Visualization Suite",
    description: "Interactive data exploration and visualization toolkit for complex datasets with statistical analysis capabilities.",
    category: "Data Science",
    mockupType: "dashboard",
    technologies: ["Python", "Plotly", "Streamlit", "Pandas"]
  },
  {
    id: 6,
    title: "Smart IoT Analytics Platform",
    description: "IoT data processing and analytics platform with real-time monitoring and predictive maintenance features.",
    category: "IoT",
    mockupType: "web",
    technologies: ["Node.js", "InfluxDB", "Grafana", "MQTT"]
  }
];

const ProjectCard = ({ project, index }: { project: Project; index: number }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(cardRef, { 
    once: true, 
    margin: "-100px 0px -100px 0px" 
  });

  const getMockupContent = (type: string) => {
    const baseClasses = "w-full h-full rounded-lg overflow-hidden relative";
    
    switch (type) {
      case 'dashboard':
        return (
          <div className={`${baseClasses} bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900`}>
            <div className="h-8 bg-gradient-to-r from-gray-800 to-gray-700 flex items-center px-3 space-x-2">
              <div className="w-2 h-2 rounded-full bg-red-500"></div>
              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
            </div>
            <div className="p-3 space-y-2">
              <div className="flex space-x-2">
                <div className="flex-1 h-12 bg-gradient-to-r from-blue-600/30 to-purple-600/30 rounded border border-blue-500/20"></div>
                <div className="flex-1 h-12 bg-gradient-to-r from-green-600/30 to-teal-600/30 rounded border border-green-500/20"></div>
              </div>
              <div className="h-16 bg-gradient-to-r from-indigo-600/20 to-blue-600/20 rounded border border-indigo-500/20"></div>
              <div className="flex space-x-1">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="flex-1 bg-gradient-to-t from-blue-500/40 to-transparent rounded-sm" 
                       style={{ height: `${Math.random() * 20 + 10}px` }}></div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 'mobile':
        return (
          <div className={`${baseClasses} bg-gradient-to-b from-gray-900 to-black mx-auto`} style={{ width: '60%', aspectRatio: '9/16' }}>
            <div className="h-6 bg-gradient-to-r from-gray-800 to-gray-700 flex items-center justify-center">
              <div className="w-8 h-1 bg-gray-600 rounded-full"></div>
            </div>
            <div className="p-2 space-y-2 h-full">
              <div className="h-8 bg-gradient-to-r from-purple-600/30 to-blue-600/30 rounded border border-purple-500/20"></div>
              <div className="grid grid-cols-2 gap-1">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="aspect-square bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded border border-blue-400/20"></div>
                ))}
              </div>
              <div className="flex-1 bg-gradient-to-t from-green-500/20 to-transparent rounded border border-green-400/20"></div>
            </div>
          </div>
        );
      
      case 'web':
        return (
          <div className={`${baseClasses} bg-gradient-to-br from-gray-900 via-blue-900/20 to-gray-900`}>
            <div className="h-6 bg-gradient-to-r from-gray-800 to-gray-700 flex items-center px-2 space-x-1">
              <div className="flex space-x-1">
                <div className="w-1.5 h-1.5 rounded-full bg-red-500"></div>
                <div className="w-1.5 h-1.5 rounded-full bg-yellow-500"></div>
                <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 h-2 bg-gray-600 rounded-sm mx-2"></div>
            </div>
            <div className="p-2 space-y-1">
              <div className="h-6 bg-gradient-to-r from-blue-600/30 to-purple-600/30 rounded border border-blue-500/20"></div>
              <div className="grid grid-cols-3 gap-1">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-8 bg-gradient-to-br from-indigo-500/20 to-blue-500/20 rounded border border-indigo-400/20"></div>
                ))}
              </div>
              <div className="h-12 bg-gradient-to-r from-green-500/20 to-teal-500/20 rounded border border-green-400/20"></div>
            </div>
          </div>
        );
      
      case 'ai':
        return (
          <div className={`${baseClasses} bg-gradient-to-br from-purple-900/40 via-black to-blue-900/40`}>
            <div className="p-2 space-y-1">
              <div className="h-4 bg-gradient-to-r from-purple-600/40 to-pink-600/40 rounded border border-purple-500/30"></div>
              <div className="flex space-x-1">
                <div className="w-1/3 space-y-1">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-2 bg-gradient-to-r from-cyan-500/30 to-blue-500/30 rounded border border-cyan-400/20"></div>
                  ))}
                </div>
                <div className="flex-1 h-16 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded border border-purple-500/20 relative">
                  <div className="absolute inset-1 flex items-center justify-center">
                    <div className="grid grid-cols-3 gap-1 w-full h-full">
                      {[...Array(9)].map((_, i) => (
                        <div key={i} className="w-1 h-1 bg-cyan-400/60 rounded-full animate-pulse" 
                             style={{ animationDelay: `${i * 0.1}s` }}></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              <div className="h-6 bg-gradient-to-r from-green-500/30 to-emerald-500/30 rounded border border-green-400/20"></div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className={`${baseClasses} bg-gradient-to-br from-gray-800 to-gray-900`}>
            <div className="flex items-center justify-center h-full">
              <div className="w-12 h-12 border-2 border-blue-500/50 rounded-lg"></div>
            </div>
          </div>
        );
    }
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ 
        opacity: 0, 
        y: 100, 
        filter: "blur(10px)" 
      }}
      animate={isInView ? { 
        opacity: 1, 
        y: 0, 
        filter: "blur(0px)" 
      } : {}}
      transition={{ 
        duration: 0.8, 
        delay: index * 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      className="group relative"
    >
      <div className="relative bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden hover:bg-white/10 transition-all duration-500 hover:scale-105 hover:border-white/20 shadow-lg hover:shadow-2xl">
        <div className="relative h-48 p-4 bg-gradient-to-br from-slate-900/50 to-slate-800/50">
          <div className="h-full w-full">
            {getMockupContent(project.mockupType)}
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>

        <div className="p-6 space-y-4">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-semibold text-white leading-tight group-hover:text-blue-300 transition-colors duration-300">
              {project.title}
            </h3>
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs px-3 py-1 rounded-full shadow-md flex-shrink-0 ml-2">
              {project.category}
            </span>
          </div>

          <p className="text-gray-300 text-sm leading-relaxed line-clamp-3">
            {project.description}
          </p>

          <div className="flex flex-wrap gap-2">
            {project.technologies.map((tech, techIndex) => (
              <span 
                key={techIndex}
                className="text-xs px-2 py-1 bg-white/10 text-gray-200 rounded border border-white/20 hover:bg-white/20 transition-colors duration-200"
              >
                {tech}
              </span>
            ))}
          </div>

          <div className="pt-2">
            <button className="w-full py-2 px-4 bg-gradient-to-r from-blue-600/80 to-purple-600/80 hover:from-blue-500 hover:to-purple-500 text-white text-sm font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:scale-105 border border-blue-500/30">
              View Project
            </button>
          </div>
        </div>

        <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 animate-pulse"></div>
        </div>
      </div>
    </motion.div>
  );
};

export default function ProjectsSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const isTitleInView = useInView(titleRef, { once: true, margin: "-50px" });

  return (
    <section
      ref={sectionRef}
      id="projects"
      className="min-h-screen py-20 px-4 relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Title */}
        <motion.div
          ref={titleRef}
          initial={{ opacity: 0, y: 50 }}
          animate={isTitleInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-black mb-4">
            Featured Projects
          </h2>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Explore my portfolio of AI/ML projects, from computer vision applications to NLP systems and data analytics platforms.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <ProjectCard key={project.id} project={project} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
}
