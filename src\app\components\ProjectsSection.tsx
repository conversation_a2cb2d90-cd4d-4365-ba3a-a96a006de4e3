'use client';

import { useRef } from 'react';
import { motion, useInView, useScroll, useTransform } from 'framer-motion';

interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  mockupType: 'dashboard' | 'mobile' | 'web' | 'ai';
  technologies: string[];
}

const projects: Project[] = [
  {
    id: 1,
    title: "AI-Powered Analytics Dashboard",
    description: "Real-time ML insights and predictive analytics for business intelligence.",
    category: "AI/ML",
    mockupType: "dashboard",
    technologies: ["Python", "TensorFlow", "React"]
  },
  {
    id: 2,
    title: "Computer Vision Mobile App",
    description: "Real-time object detection and image classification on mobile devices.",
    category: "Mobile",
    mockupType: "mobile",
    technologies: ["React Native", "OpenCV", "PyTorch"]
  },
  {
    id: 3,
    title: "NLP Recommendation Engine",
    description: "Personalized content recommendations using transformer models.",
    category: "NLP",
    mockupType: "web",
    technologies: ["BERT", "FastAPI", "PostgreSQL"]
  },
  {
    id: 4,
    title: "Deep Learning Research Platform",
    description: "Collaborative environment for automated model training and evaluation.",
    category: "Research",
    mockupType: "ai",
    technologies: ["PyTorch", "MLflow", "Kubernetes"]
  },
  {
    id: 5,
    title: "Data Science Visualization Suite",
    description: "Interactive data exploration with advanced statistical analysis.",
    category: "Data Science",
    mockupType: "dashboard",
    technologies: ["Python", "Plotly", "Streamlit"]
  },
  {
    id: 6,
    title: "Smart IoT Analytics Platform",
    description: "Real-time IoT monitoring with predictive maintenance features.",
    category: "IoT",
    mockupType: "web",
    technologies: ["Node.js", "InfluxDB", "Grafana"]
  }
];

const LaptopProjectCard = ({ project, index }: { project: Project; index: number }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  });

  // Calculate laptop lid rotation based on scroll progress
  const lidRotation = useTransform(scrollYProgress, [0.2, 0.8], [-90, 0]);
  const cardOpacity = useTransform(scrollYProgress, [0.1, 0.3, 0.7, 0.9], [0, 1, 1, 0]);
  const cardScale = useTransform(scrollYProgress, [0.2, 0.5, 0.8], [0.8, 1, 0.8]);

  const getLaptopScreen = (type: string) => {
    const baseClasses = "w-full h-full rounded-t-lg overflow-hidden relative bg-black";

    switch (type) {
      case 'dashboard':
        return (
          <div className={`${baseClasses}`}>
            <div className="p-4 space-y-3 h-full">
              <div className="flex space-x-3">
                <div className="flex-1 h-8 bg-gradient-to-r from-blue-600/40 to-purple-600/40 rounded"></div>
                <div className="flex-1 h-8 bg-gradient-to-r from-green-600/40 to-teal-600/40 rounded"></div>
              </div>
              <div className="h-12 bg-gradient-to-r from-indigo-600/30 to-blue-600/30 rounded"></div>
              <div className="flex space-x-2">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="flex-1 bg-gradient-to-t from-blue-500/50 to-transparent rounded"
                       style={{ height: `${Math.random() * 30 + 20}px` }}></div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'mobile':
        return (
          <div className={`${baseClasses} flex items-center justify-center`}>
            <div className="w-32 h-56 bg-gradient-to-b from-gray-800 to-black rounded-lg border border-gray-600 p-2">
              <div className="h-6 bg-gradient-to-r from-purple-600/40 to-blue-600/40 rounded mb-2"></div>
              <div className="grid grid-cols-2 gap-1 mb-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="aspect-square bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded"></div>
                ))}
              </div>
              <div className="flex-1 bg-gradient-to-t from-green-500/30 to-transparent rounded"></div>
            </div>
          </div>
        );

      case 'web':
        return (
          <div className={`${baseClasses}`}>
            <div className="h-6 bg-gradient-to-r from-gray-700 to-gray-600 flex items-center px-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
            </div>
            <div className="p-4 space-y-2">
              <div className="h-8 bg-gradient-to-r from-blue-600/40 to-purple-600/40 rounded"></div>
              <div className="grid grid-cols-3 gap-2">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-6 bg-gradient-to-br from-indigo-500/30 to-blue-500/30 rounded"></div>
                ))}
              </div>
              <div className="h-10 bg-gradient-to-r from-green-500/30 to-teal-500/30 rounded"></div>
            </div>
          </div>
        );

      case 'ai':
        return (
          <div className={`${baseClasses}`}>
            <div className="p-4 space-y-3">
              <div className="h-6 bg-gradient-to-r from-purple-600/50 to-pink-600/50 rounded"></div>
              <div className="flex space-x-3">
                <div className="w-1/3 space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-3 bg-gradient-to-r from-cyan-500/40 to-blue-500/40 rounded"></div>
                  ))}
                </div>
                <div className="flex-1 h-20 bg-gradient-to-br from-purple-600/30 to-blue-600/30 rounded relative">
                  <div className="absolute inset-2 grid grid-cols-4 gap-1">
                    {[...Array(12)].map((_, i) => (
                      <div key={i} className="w-1 h-1 bg-cyan-400/70 rounded-full animate-pulse"
                           style={{ animationDelay: `${i * 0.15}s` }}></div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="h-8 bg-gradient-to-r from-green-500/40 to-emerald-500/40 rounded"></div>
            </div>
          </div>
        );

      default:
        return (
          <div className={`${baseClasses} flex items-center justify-center`}>
            <div className="w-16 h-16 border-2 border-blue-500/50 rounded-lg"></div>
          </div>
        );
    }
  };

  return (
    <motion.div
      ref={cardRef}
      style={{
        opacity: cardOpacity,
        scale: cardScale,
      }}
      className="w-full h-screen flex items-center justify-center px-4"
    >
      {/* Laptop Container */}
      <div
        className="relative laptop-perspective"
        style={{
          transformStyle: "preserve-3d"
        }}
      >
        {/* Laptop Base */}
        <div className="relative w-96 h-6 bg-gradient-to-r from-gray-800 to-gray-700 rounded-lg shadow-2xl">
          {/* Laptop Hinge */}
          <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-gray-700 to-gray-600 rounded-t-lg"></div>

          {/* Laptop Keyboard Area */}
          <div className="absolute top-2 left-4 right-4 bottom-1 bg-gradient-to-br from-gray-900 to-black rounded">
            <div className="p-2 grid grid-cols-12 gap-1">
              {[...Array(36)].map((_, i) => (
                <div key={i} className="h-1 bg-gray-600 rounded-sm opacity-60"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Laptop Screen (Lid) */}
        <motion.div
          style={{
            rotateX: lidRotation,
            transformOrigin: "bottom center",
          }}
          className="absolute bottom-6 left-0 w-96 h-64 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-2xl border-4 border-gray-700 laptop-screen-glow"
        >
          {/* Screen Bezel */}
          <div className="absolute inset-2 bg-black rounded border border-gray-600">
            {/* Screen Content */}
            <div className="absolute inset-1 rounded overflow-hidden">
              {getLaptopScreen(project.mockupType)}
            </div>
          </div>

          {/* Project Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-4 rounded-b">
            <div className="space-y-2">
              <div className="flex justify-between items-start">
                <h3 className="text-white font-semibold text-lg leading-tight">
                  {project.title}
                </h3>
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs px-2 py-1 rounded-full flex-shrink-0 ml-2">
                  {project.category}
                </span>
              </div>

              <p className="text-gray-300 text-sm">
                {project.description}
              </p>

              <div className="flex flex-wrap gap-1">
                {project.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="text-xs px-2 py-1 bg-white/20 text-gray-200 rounded border border-white/30"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Screen Glow Effect */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 animate-pulse pointer-events-none"></div>
        </motion.div>

        {/* Laptop Shadow */}
        <div className="absolute top-6 left-2 right-2 h-2 bg-black/20 rounded-full blur-sm"></div>
      </div>
    </motion.div>
  );
};

export default function ProjectsSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const isTitleInView = useInView(titleRef, { once: true, margin: "-50px" });

  return (
    <section
      ref={sectionRef}
      id="projects"
      className="relative snap-section"
    >
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-purple-500/3 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Section Title */}
      <motion.div
        ref={titleRef}
        initial={{ opacity: 0, y: 50 }}
        animate={isTitleInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="h-screen flex flex-col items-center justify-center text-center px-4 relative z-10 snap-section"
      >
        <h2 className="text-4xl md:text-6xl font-bold text-black mb-6">
          Featured Projects
        </h2>
        <p className="text-gray-300 text-xl max-w-3xl mx-auto mb-8">
          Explore my portfolio of AI/ML projects through interactive laptop demonstrations
        </p>
        <div className="text-gray-400 text-sm animate-bounce">
          Scroll down to open each project
        </div>
      </motion.div>

      {/* Projects Container with Scroll Snap */}
      <div className="relative">
        {projects.map((project, index) => (
          <div
            key={project.id}
            className="h-screen snap-start snap-always relative snap-section"
            style={{
              scrollSnapAlign: "start",
            }}
          >
            <LaptopProjectCard project={project} index={index} />

            {/* Project Counter */}
            <div className="absolute top-8 right-8 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm">
              {index + 1} / {projects.length}
            </div>
          </div>
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex space-x-2">
          {projects.map((_, index) => (
            <div
              key={index}
              className="w-2 h-2 rounded-full bg-white/30 hover:bg-white/60 transition-colors duration-200 cursor-pointer"
            ></div>
          ))}
        </div>
      </div>
    </section>
  );
}
