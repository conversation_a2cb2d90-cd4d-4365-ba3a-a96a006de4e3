'use client';

import { useRef } from 'react';
import { motion, useInView, useScroll, useTransform } from 'framer-motion';

interface Project {
  id: number;
  title: string;
  description: string;
  category: string;
  mockupType: 'dashboard' | 'mobile' | 'web' | 'ai';
  technologies: string[];
}

const projects: Project[] = [
  {
    id: 1,
    title: "AI-Powered Analytics Dashboard",
    description: "Real-time ML insights and predictive analytics for business intelligence.",
    category: "AI/ML",
    mockupType: "dashboard",
    technologies: ["Python", "TensorFlow", "React"]
  },
  {
    id: 2,
    title: "Computer Vision Mobile App",
    description: "Real-time object detection and image classification on mobile devices.",
    category: "Mobile",
    mockupType: "mobile",
    technologies: ["React Native", "OpenCV", "PyTorch"]
  },
  {
    id: 3,
    title: "NLP Recommendation Engine",
    description: "Personalized content recommendations using transformer models.",
    category: "NLP",
    mockupType: "web",
    technologies: ["BERT", "FastAPI", "PostgreSQL"]
  },
  {
    id: 4,
    title: "Deep Learning Research Platform",
    description: "Collaborative environment for automated model training and evaluation.",
    category: "Research",
    mockupType: "ai",
    technologies: ["PyTorch", "MLflow", "Kubernetes"]
  },
  {
    id: 5,
    title: "Data Science Visualization Suite",
    description: "Interactive data exploration with advanced statistical analysis.",
    category: "Data Science",
    mockupType: "dashboard",
    technologies: ["Python", "Plotly", "Streamlit"]
  },
  {
    id: 6,
    title: "Smart IoT Analytics Platform",
    description: "Real-time IoT monitoring with predictive maintenance features.",
    category: "IoT",
    mockupType: "web",
    technologies: ["Node.js", "InfluxDB", "Grafana"]
  }
];

const LaptopProjectCard = ({ project, index }: { project: Project; index: number }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"]
  });

  // Calculate laptop lid rotation based on scroll progress
  const lidRotation = useTransform(scrollYProgress, [0.2, 0.8], [-90, 0]);
  const cardOpacity = useTransform(scrollYProgress, [0.1, 0.3, 0.7, 0.9], [0, 1, 1, 0]);
  const cardScale = useTransform(scrollYProgress, [0.2, 0.5, 0.8], [0.8, 1, 0.8]);

  const getLaptopScreen = (type: string) => {
    const baseClasses = "w-full h-full rounded-xl overflow-hidden relative bg-gradient-to-br from-slate-900/50 to-slate-800/50";

    switch (type) {
      case 'dashboard':
        return (
          <div className={`${baseClasses}`}>
            {/* Browser Header */}
            <div className="h-12 bg-gradient-to-r from-gray-800/80 to-gray-700/80 flex items-center px-6 space-x-3 border-b border-white/10">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 h-6 bg-gray-600/50 rounded mx-4"></div>
            </div>
            {/* Dashboard Content */}
            <div className="p-6 space-y-4 h-full">
              <div className="flex space-x-4">
                <div className="flex-1 h-16 bg-gradient-to-r from-blue-600/40 to-purple-600/40 rounded-lg border border-blue-500/30"></div>
                <div className="flex-1 h-16 bg-gradient-to-r from-green-600/40 to-teal-600/40 rounded-lg border border-green-500/30"></div>
                <div className="flex-1 h-16 bg-gradient-to-r from-orange-600/40 to-red-600/40 rounded-lg border border-orange-500/30"></div>
              </div>
              <div className="h-24 bg-gradient-to-r from-indigo-600/30 to-blue-600/30 rounded-lg border border-indigo-500/20"></div>
              <div className="flex space-x-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="flex-1 bg-gradient-to-t from-blue-500/50 to-transparent rounded-lg border border-blue-400/20"
                       style={{ height: `${Math.random() * 40 + 30}px` }}></div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'mobile':
        return (
          <div className={`${baseClasses} flex items-center justify-center`}>
            <div className="w-48 h-80 bg-gradient-to-b from-gray-800/80 to-black/80 rounded-2xl border-2 border-gray-600/50 p-4 shadow-2xl">
              {/* Mobile Status Bar */}
              <div className="h-6 bg-gradient-to-r from-purple-600/50 to-blue-600/50 rounded-lg mb-4 border border-purple-500/30"></div>
              {/* App Icons Grid */}
              <div className="grid grid-cols-3 gap-3 mb-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="aspect-square bg-gradient-to-br from-blue-500/40 to-purple-500/40 rounded-xl border border-blue-400/30"></div>
                ))}
              </div>
              {/* Main Content Area */}
              <div className="flex-1 bg-gradient-to-t from-green-500/30 to-transparent rounded-lg border border-green-400/20"></div>
              {/* Bottom Navigation */}
              <div className="h-8 bg-gradient-to-r from-gray-700/50 to-gray-600/50 rounded-lg mt-4 border border-gray-500/30"></div>
            </div>
          </div>
        );

      case 'web':
        return (
          <div className={`${baseClasses}`}>
            {/* Browser Header */}
            <div className="h-12 bg-gradient-to-r from-gray-800/80 to-gray-700/80 flex items-center px-6 border-b border-white/10">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 h-6 bg-gray-600/50 rounded mx-4"></div>
            </div>
            {/* Web Content */}
            <div className="p-6 space-y-4">
              {/* Navigation */}
              <div className="h-12 bg-gradient-to-r from-blue-600/40 to-purple-600/40 rounded-lg border border-blue-500/30"></div>
              {/* Content Grid */}
              <div className="grid grid-cols-4 gap-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-12 bg-gradient-to-br from-indigo-500/30 to-blue-500/30 rounded-lg border border-indigo-400/20"></div>
                ))}
              </div>
              {/* Main Content */}
              <div className="h-20 bg-gradient-to-r from-green-500/30 to-teal-500/30 rounded-lg border border-green-400/20"></div>
            </div>
          </div>
        );

      case 'ai':
        return (
          <div className={`${baseClasses}`}>
            <div className="p-6 space-y-4 h-full">
              {/* AI Header */}
              <div className="h-12 bg-gradient-to-r from-purple-600/50 to-pink-600/50 rounded-lg border border-purple-500/40"></div>
              {/* AI Interface */}
              <div className="flex space-x-4 h-32">
                <div className="w-1/3 space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="h-6 bg-gradient-to-r from-cyan-500/40 to-blue-500/40 rounded border border-cyan-400/30"></div>
                  ))}
                </div>
                <div className="flex-1 bg-gradient-to-br from-purple-600/30 to-blue-600/30 rounded-lg border border-purple-500/20 relative">
                  {/* Neural Network Visualization */}
                  <div className="absolute inset-4 grid grid-cols-6 gap-2">
                    {[...Array(18)].map((_, i) => (
                      <div key={i} className="w-2 h-2 bg-cyan-400/70 rounded-full animate-pulse"
                           style={{ animationDelay: `${i * 0.1}s` }}></div>
                    ))}
                  </div>
                </div>
              </div>
              {/* Results */}
              <div className="h-16 bg-gradient-to-r from-green-500/40 to-emerald-500/40 rounded-lg border border-green-400/30"></div>
            </div>
          </div>
        );

      default:
        return (
          <div className={`${baseClasses} flex items-center justify-center`}>
            <div className="w-24 h-24 border-2 border-blue-500/50 rounded-xl"></div>
          </div>
        );
    }
  };

  return (
    <motion.div
      ref={cardRef}
      style={{
        opacity: cardOpacity,
        scale: cardScale,
      }}
      className="w-full h-screen flex items-center justify-center px-4"
    >
      {/* Glassmorphism Card Container */}
      <div
        className="relative laptop-perspective"
        style={{
          transformStyle: "preserve-3d"
        }}
      >
        {/* Glassmorphism Project Card with Laptop Lid Opening Effect */}
        <motion.div
          style={{
            rotateX: lidRotation,
            transformOrigin: "bottom center",
          }}
          className="relative w-[800px] h-[500px] bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl overflow-hidden group hover:bg-white/15 transition-all duration-500"
        >
          {/* Mockup Interface Area */}
          <div className="relative h-80 p-6 bg-gradient-to-br from-slate-900/30 to-slate-800/30">
            <div className="h-full w-full rounded-xl overflow-hidden border border-white/10">
              {getLaptopScreen(project.mockupType)}
            </div>

            {/* Overlay gradient for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
          </div>

          {/* Project Information */}
          <div className="p-8 space-y-6 bg-gradient-to-t from-black/20 to-transparent">
            {/* Header */}
            <div className="flex justify-between items-start">
              <h3 className="text-2xl font-bold text-white leading-tight group-hover:text-blue-300 transition-colors duration-300">
                {project.title}
              </h3>
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm px-4 py-2 rounded-full shadow-md flex-shrink-0 ml-4">
                {project.category}
              </span>
            </div>

            {/* Description */}
            <p className="text-gray-200 text-lg leading-relaxed">
              {project.description}
            </p>

            {/* Technologies */}
            <div className="flex flex-wrap gap-3">
              {project.technologies.map((tech, techIndex) => (
                <span
                  key={techIndex}
                  className="text-sm px-4 py-2 bg-white/15 text-gray-200 rounded-lg border border-white/25 hover:bg-white/25 transition-colors duration-200 backdrop-blur-sm"
                >
                  {tech}
                </span>
              ))}
            </div>

            {/* Action Button */}
            <div className="pt-4">
              <button className="px-8 py-3 bg-gradient-to-r from-blue-600/80 to-purple-600/80 hover:from-blue-500 hover:to-purple-500 text-white text-lg font-medium rounded-xl transition-all duration-300 hover:shadow-lg hover:scale-105 border border-blue-500/30 backdrop-blur-sm">
                View Project Details
              </button>
            </div>
          </div>

          {/* Animated border glow effect */}
          <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 animate-pulse"></div>
          </div>

          {/* Glassmorphism enhancement */}
          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 via-transparent to-white/5 pointer-events-none"></div>
        </motion.div>

        {/* Card Shadow */}
        <div className="absolute top-4 left-4 right-4 bottom-4 bg-black/10 rounded-2xl blur-xl -z-10"></div>
      </div>
    </motion.div>
  );
};

export default function ProjectsSection() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const isTitleInView = useInView(titleRef, { once: true, margin: "-50px" });

  return (
    <section
      ref={sectionRef}
      id="projects"
      className="relative snap-section"
    >
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-purple-500/3 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Section Title */}
      <motion.div
        ref={titleRef}
        initial={{ opacity: 0, y: 50 }}
        animate={isTitleInView ? { opacity: 1, y: 0 } : {}}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="h-screen flex flex-col items-center justify-center text-center px-4 relative z-10 snap-section"
      >
        <h2 className="text-4xl md:text-6xl font-bold text-black mb-6">
          Featured Projects
        </h2>
        <p className="text-gray-300 text-xl max-w-3xl mx-auto mb-8">
          Explore my portfolio of AI/ML projects through interactive laptop demonstrations
        </p>
        <div className="text-gray-400 text-sm animate-bounce">
          Scroll down to open each project
        </div>
      </motion.div>

      {/* Projects Container with Scroll Snap */}
      <div className="relative">
        {projects.map((project, index) => (
          <div
            key={project.id}
            className="h-screen snap-start snap-always relative snap-section"
            style={{
              scrollSnapAlign: "start",
            }}
          >
            <LaptopProjectCard project={project} index={index} />

            {/* Project Counter */}
            <div className="absolute top-8 right-8 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm">
              {index + 1} / {projects.length}
            </div>
          </div>
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex space-x-2">
          {projects.map((_, index) => (
            <div
              key={index}
              className="w-2 h-2 rounded-full bg-white/30 hover:bg-white/60 transition-colors duration-200 cursor-pointer"
            ></div>
          ))}
        </div>
      </div>
    </section>
  );
}
