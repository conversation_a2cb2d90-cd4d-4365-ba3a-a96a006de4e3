'use client';

import { useState } from 'react';
import { FaQuoteLeft, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const testimonials = [
  {
    id: 1,
    quote: "<PERSON>'s NLP expertise helped us reduce customer support response time by 40%. Her models were both accurate and efficient.",
    author: "<PERSON>",
    role: "CTO, Tech Innovations Inc.",
    avatar: "SJ"
  },
  {
    id: 2,
    quote: "The computer vision system Jane developed for our manufacturing line has improved defect detection accuracy to 99.8%.",
    author: "<PERSON>",
    role: "Operations Director, Precision Manufacturing",
    avatar: "MC"
  },
  {
    id: 3,
    quote: "<PERSON>'s recommender system increased our e-commerce conversion rate by 22%. She consistently delivers innovative solutions.",
    author: "<PERSON>",
    role: "Product Lead, Global Retail",
    avatar: "AR"
  },
  {
    id: 4,
    quote: "Working with <PERSON> on our time series forecasting project was a game-changer. Her models helped us optimize inventory and reduce waste.",
    author: "<PERSON>",
    role: "Supply Chain Manager, FreshGoods",
    avatar: "DK"
  }
];

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section id="testimonials" className="py-20 relative">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold glossy-black-text">
            Testimonials & Collaborations
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative glass rounded-2xl p-8 md:p-12 shadow-xl">
            <FaQuoteLeft className="text-5xl text-purple-500 dark:text-purple-400 opacity-30 absolute top-6 left-6" />

            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="flex-shrink-0">
                <div className="testimonial-avatar w-24 h-24 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                  {currentTestimonial.avatar}
                </div>
              </div>

              <div>
                <p className="text-xl italic text-gray-700 dark:text-gray-300 mb-6">
                  "{currentTestimonial.quote}"
                </p>

                <div>
                  <p className="text-lg font-semibold text-oxford-blue dark:text-white">
                    {currentTestimonial.author}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">
                    {currentTestimonial.role}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-center mt-8 gap-4">
              <button
                onClick={prevTestimonial}
                className="glass-button p-4 rounded-full text-purple-600 dark:text-purple-400 hover:scale-110 transition-all duration-300"
                aria-label="Previous testimonial"
              >
                <FaChevronLeft />
              </button>

              <div className="flex items-center gap-3">
                {testimonials.map((_, index) => (
                  <div
                    key={index}
                    className={`w-4 h-4 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? 'bg-gradient-to-r from-purple-500 to-indigo-500 scale-125 shadow-lg'
                        : 'bg-white/30 dark:bg-black/30 hover:bg-white/50 dark:hover:bg-black/50 hover:scale-110'
                    }`}
                  />
                ))}
              </div>

              <button
                onClick={nextTestimonial}
                className="glass-button p-4 rounded-full text-purple-600 dark:text-purple-400 hover:scale-110 transition-all duration-300"
                aria-label="Next testimonial"
              >
                <FaChevronRight />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}