@import "tailwindcss";

html,
body {
  font-family: ui-sans-serif, system-ui, sans-serif;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* Scroll snap for main page sections */
html {
  scroll-snap-type: y mandatory;
}

.snap-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

/* Ensure text remains readable on dynamic backgrounds */
body {
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Dynamic background transitions */
.dynamic-bg-transition {
  transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 3D Background integration */
.bg-3d-integration {
  mix-blend-mode: soft-light;
  filter: blur(0.5px);
}

/* Clean professional styles - removed gaming UI */

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Glassmorphism text effects with subtle glow */
.glass-text {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1rem 2rem;
  text-shadow:
    0 0 3px rgba(255, 255, 255, 0.3),
    0 0 6px rgba(147, 51, 234, 0.2);
  animation: subtle-glow 4s ease-in-out infinite alternate;
}

.glass-text-small {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 0.5rem 1rem;
  text-shadow:
    0 0 2px rgba(255, 255, 255, 0.4),
    0 0 4px rgba(147, 51, 234, 0.2);
  animation: subtle-glow-small 3s ease-in-out infinite alternate;
}

@keyframes subtle-glow {
  0% {
    text-shadow:
      0 0 3px rgba(255, 255, 255, 0.3),
      0 0 6px rgba(147, 51, 234, 0.2);
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.05),
      inset 0 0 20px rgba(255, 255, 255, 0.05);
  }
  100% {
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.5),
      0 0 10px rgba(147, 51, 234, 0.3);
    box-shadow:
      0 0 30px rgba(255, 255, 255, 0.1),
      inset 0 0 30px rgba(255, 255, 255, 0.08);
  }
}

@keyframes subtle-glow-small {
  0% {
    text-shadow:
      0 0 2px rgba(255, 255, 255, 0.4),
      0 0 4px rgba(147, 51, 234, 0.2);
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.03),
      inset 0 0 15px rgba(255, 255, 255, 0.03);
  }
  100% {
    text-shadow:
      0 0 4px rgba(255, 255, 255, 0.6),
      0 0 8px rgba(147, 51, 234, 0.3);
    box-shadow:
      0 0 25px rgba(255, 255, 255, 0.08),
      inset 0 0 25px rgba(255, 255, 255, 0.05);
  }
}

/* Enhanced button styles */
.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Star Animation Button Effects */
.star-button {
  position: relative;
  padding: 12px 35px;
  background: #6666ff;
  font-size: 17px;
  font-weight: 500;
  color: #000000;
  border: 3px solid #6666ff;
  border-radius: 50px;
  box-shadow: 0 0 0 #fec1958c;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  overflow: hidden;
}

.star-1 {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 25px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0.05, 0.83, 0.43, 0.96);
}

.star-2 {
  position: absolute;
  top: 45%;
  left: 45%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-3 {
  position: absolute;
  top: 40%;
  left: 40%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-4 {
  position: absolute;
  top: 20%;
  left: 40%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-5 {
  position: absolute;
  top: 25%;
  left: 45%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.6s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-6 {
  position: absolute;
  top: 5%;
  left: 50%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s ease;
}

.star-button:hover {
  background: transparent;
  color: #6666ff;
  box-shadow: 0 0 25px #fec1958c;
}

.star-button:hover .star-1 {
  position: absolute;
  top: -80%;
  left: -30%;
  width: 25px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-2 {
  position: absolute;
  top: -25%;
  left: 10%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-3 {
  position: absolute;
  top: 55%;
  left: 25%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-4 {
  position: absolute;
  top: 30%;
  left: 80%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-5 {
  position: absolute;
  top: 25%;
  left: 115%;
  width: 15px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover .star-6 {
  position: absolute;
  top: 5%;
  left: 60%;
  width: 5px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.fil0 {
  fill: #fffdef;
}

/* Custom color hover effects */
.contact-icon {
  background-color: transparent;
  border: 2px solid #ff0000;
  color: #ff0000;
  transition: all 0.3s ease;
}

.contact-icon:hover {
  border-color: #ff1a8c;
  color: #ff1a8c;
  transform: scale(1.1);
}

.testimonial-avatar {
  background: linear-gradient(135deg, #6666ff, #0044cc);
  transition: all 0.3s ease;
}

.project-filter-active {
  background: linear-gradient(135deg, #6666ff, #0044cc);
  border-color: #0044cc;
}

/* Animated Send Message Button */
.Btn-Container {
  display: flex;
  width: 170px;
  height: fit-content;
  background-color: #1d2129;
  border-radius: 40px;
  box-shadow: 0px 5px 10px #bebebe;
  justify-content: space-between;
  align-items: center;
  border: none;
  cursor: pointer;
}

.icon-Container {
  width: 45px;
  height: 45px;
  background-color: #f59aff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 3px solid #1d2129;
}

.text {
  width: calc(170px - 45px);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9em;
  letter-spacing: 1.2px;
}

.icon-Container svg {
  transition-duration: 1.5s;
}

.Btn-Container:hover .icon-Container svg {
  transition-duration: 1.5s;
  animation: arrow 1s linear infinite;
}

@keyframes arrow {
  0% {
    opacity: 0;
    margin-left: 0px;
  }
  100% {
    opacity: 1;
    margin-left: 10px;
  }
}

/* Performance optimizations for scroll transitions */
.transform-gpu {
  transform: translateZ(0);
  will-change: transform, opacity, filter;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth scroll transition container */
.scroll-transition-container {
  contain: layout style paint;
  transform: translateZ(0);
  position: relative;
  overflow: visible;
}

/* Optimize 3D transforms */
.space-transition {
  transform-style: preserve-3d;
  will-change: transform, opacity, width, height, position;
  backface-visibility: hidden;
  transition: width 0.3s ease-out, height 0.3s ease-out, position 0.3s ease-out;
}

/* Viewport expansion effect for falling through space */
.space-transition.expanding {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  overflow: hidden;
}

/* Portal dimension container - contains all sections after wormhole */
.portal-dimension-container {
  position: relative;
  background: linear-gradient(180deg,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(26, 26, 46, 0.9) 20%,
    rgba(0, 68, 204, 0.1) 50%,
    rgba(0, 0, 0, 0.95) 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Wormhole portal effects */
.wormhole-active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: radial-gradient(circle at center,
    transparent 0%,
    rgba(102, 102, 255, 0.3) 30%,
    rgba(0, 68, 204, 0.6) 60%,
    rgba(0, 0, 0, 0.9) 100%);
  animation: portalPulse 2s ease-in-out infinite alternate;
}

@keyframes portalPulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.05);
    opacity: 1;
  }
}





/* Clean scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Glossy black text effect */
.glossy-black-text {
  background: linear-gradient(135deg, #1a1a1a 0%, #000000 25%, #2d2d2d 50%, #000000 75%, #1a1a1a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow:
    0 1px 0 rgba(255, 255, 255, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 0 10px rgba(255, 255, 255, 0.05);
  filter: drop-shadow(0 1px 2px rgba(255, 255, 255, 0.1));
}

/* Glossy black text without shadows */
.glossy-black-text-no-shadow {
  background: linear-gradient(135deg, #1a1a1a 0%, #000000 25%, #2d2d2d 50%, #000000 75%, #1a1a1a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Pearl white Nunito Sans headings */
.pearl-white-heading {
  font-family: 'Nunito Sans', sans-serif;
  color: #f8f6f0;
  font-weight: 800;
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.3),
    0 0 8px rgba(248, 246, 240, 0.1);
  letter-spacing: -0.025em;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
}

/* Animated text loader effect for skills */
.skills-loader {
  color: rgb(124, 124, 124);
  font-family: "Nunito Sans", sans-serif;
  font-weight: 600;
  font-size: inherit;
  box-sizing: content-box;
  height: 1.2em;
  padding: 0;
  display: flex;
  align-items: center;
}

.skills-words {
  overflow: hidden;
  position: relative;
  height: 1.2em;
}

.skills-words::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    transparent 10%,
    transparent 30%,
    transparent 70%,
    transparent 90%
  );
  z-index: 20;
  pointer-events: none;
}

.skills-word {
  display: block;
  height: 100%;
  padding-left: 6px;
  color: #ffffff;
  font-weight: 600;
  animation: skills_slide 6s infinite;
  line-height: 1.2;
}

@keyframes skills_slide {
  0%, 16.66% {
    transform: translateY(0%);
  }

  20%, 36.66% {
    transform: translateY(-100%);
  }

  40%, 56.66% {
    transform: translateY(-200%);
  }

  60%, 76.66% {
    transform: translateY(-300%);
  }

  80%, 96.66% {
    transform: translateY(-400%);
  }

  100% {
    transform: translateY(-500%);
  }
}

/* Mouse cursor trail effect */
body {
  cursor: none; /* Hide default cursor for immersive experience */
}

/* Show cursor on interactive elements for accessibility */
button, a, input, textarea, select, [role="button"], [tabindex] {
  cursor: pointer !important;
}

/* Cursor trail circles */
.cursor-trail-circle {
  transition: transform 0.1s ease-out;
}

/* Hanging animation for skill cards */
.hanging-skill {
  transform-origin: top center;
  animation: hanging-sway 4s ease-in-out infinite;
}

.hanging-skill-group-1 {
  animation-delay: 0s;
}

.hanging-skill-group-2 {
  animation-delay: 0.5s;
}

@keyframes hanging-sway {
  0%, 100% {
    transform: rotate(0deg) translateY(0px);
  }
  25% {
    transform: rotate(2deg) translateY(2px);
  }
  50% {
    transform: rotate(0deg) translateY(4px);
  }
  75% {
    transform: rotate(-2deg) translateY(2px);
  }
}

/* Enhanced glow pulse animation */
@keyframes glow-pulse {
  0% {
    opacity: 0.3;
    transform: scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.3;
    transform: scale(0.9);
  }
}

/* Line clamp utility for skill descriptions */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Calm font styling for skill cards */
.skill-card-content {
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Project card animations */
@keyframes projectSlideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0px);
  }
}

.project-card-animate {
  animation: projectSlideUp 0.8s ease-out forwards;
}

/* Staggered animation delays for project cards */
.project-card-delay-1 { animation-delay: 0.2s; }
.project-card-delay-2 { animation-delay: 0.4s; }
.project-card-delay-3 { animation-delay: 0.6s; }
.project-card-delay-4 { animation-delay: 0.8s; }
.project-card-delay-5 { animation-delay: 1.0s; }
.project-card-delay-6 { animation-delay: 1.2s; }

/* Glassmorphism card with laptop lid opening animation */
@keyframes cardLidOpen {
  from {
    transform: rotateX(-90deg);
    opacity: 0;
  }
  to {
    transform: rotateX(0deg);
    opacity: 1;
  }
}

.card-lid-opening {
  animation: cardLidOpen 1.2s ease-out forwards;
  transform-origin: bottom center;
  transform-style: preserve-3d;
}

/* Enhanced glassmorphism effects */
.glassmorphism-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glassmorphism-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 60px rgba(102, 102, 255, 0.2);
}

/* Scroll snap for projects */
.projects-scroll-container {
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
  overflow-y: auto;
  height: 100vh;
}

.project-snap-item {
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

/* Enhanced 3D perspective for laptop */
.laptop-perspective {
  perspective: 1200px;
  perspective-origin: center center;
}

/* Glassmorphism card glow effect */
@keyframes cardGlow {
  0% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 30px rgba(102, 102, 255, 0.2);
  }
  50% {
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 60px rgba(102, 102, 255, 0.4);
  }
  100% {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 30px rgba(102, 102, 255, 0.2);
  }
}

.glassmorphism-glow {
  animation: cardGlow 4s ease-in-out infinite;
}

/* Project card scaling and rotation effects */
.project-card-3d {
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card-3d:hover {
  transform: rotateY(5deg) rotateX(5deg) scale(1.02);
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .transform-gpu,
  .space-transition {
    transform: none !important;
    animation: none !important;
    transition: none !important;
  }

  .star-button:hover .star-1,
  .star-button:hover .star-2,
  .star-button:hover .star-3,
  .star-button:hover .star-4,
  .star-button:hover .star-5,
  .star-button:hover .star-6 {
    transform: none !important;
    animation: none !important;
  }

  .skills-word {
    animation: none !important;
    transform: none !important;
  }

  /* Show default cursor when motion is reduced */
  body {
    cursor: auto !important;
  }

  .cursor-trail-circle {
    display: none !important;
  }
}
