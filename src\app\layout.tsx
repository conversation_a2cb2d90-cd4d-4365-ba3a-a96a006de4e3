import type { Metadata } from 'next'
import './globals.css'
import AIChatWidget from './components/AIChatWidget'
import FloatingNavbar from './components/FloatingNavbar'
import DynamicBackground from './components/DynamicBackground'
import MouseCursorTrail from './components/MouseCursorTrail'

export const metadata: Metadata = {
  title: 'Jeetheshwar Aalam - AI/ML Engineer Portfolio',
  description: 'Professional portfolio showcasing AI/ML projects and expertise',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Nunito+Sans:wght@400;600;700;800;900&display=swap" rel="stylesheet" />
      </head>
      <body className="text-white relative min-h-screen" suppressHydrationWarning={true}>
        <MouseCursorTrail />
        <DynamicBackground />
        <FloatingNavbar />
        {children}
        <AIChatWidget />
      </body>
    </html>
  )
}
