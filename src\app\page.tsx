'use client'

import AboutSection from './components/AboutSection'
import ProjectsSection from './components/ProjectsSection'
import DataVisualization from './components/DataVisualization'
import TestimonialsSection from './components/TestimonialsSection'
import ContactSection from './components/ContactSection'
import StarIcon from './components/StarIcon'
import { useEffect, useState } from 'react'


export default function Home() {
  const [displayText, setDisplayText] = useState('')
  const fullText = 'Jeetheshwar Aalam • AI/ML Engineer'
  const skills = ['NLP', 'Computer Vision', 'Recommender Systems', 'Deep Learning', 'Data Science']

  const scrollToProjects = () => {
    const projectsSection = document.getElementById('projects')
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  useEffect(() => {
    let charIndex = 0
    const typingInterval = setInterval(() => {
      setDisplayText(fullText.slice(0, charIndex))
      charIndex++
      if (charIndex > fullText.length) clearInterval(typingInterval)
    }, 100)

    return () => clearInterval(typingInterval)
  }, [])

  return (
    <>
      <main id="home" className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
        {/* Background animated elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        {/* Main content container with left-right layout */}
        <div className="w-full max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 items-center relative z-10">
          {/* Left side - Content */}
          <div className="text-left lg:text-left order-2 lg:order-1">
            <div className="mb-6">
              <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4 relative">
                <span className="relative inline-block glossy-black-text">
                  {displayText}
                  <span className="ml-2 animate-pulse text-gray-700">|</span>
                </span>
              </h1>
            </div>

            <div className="mb-8">
              <div className="text-lg md:text-xl lg:text-2xl relative flex items-center flex-wrap">
                <span className="glossy-black-text-no-shadow mr-2">
                  Specializing in
                </span>
                <div className="skills-loader">
                  <div className="skills-words">
                    <span className="skills-word">{skills[0]}</span>
                    <span className="skills-word">{skills[1]}</span>
                    <span className="skills-word">{skills[2]}</span>
                    <span className="skills-word">{skills[3]}</span>
                    <span className="skills-word">{skills[4]}</span>
                    <span className="skills-word">{skills[0]}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative group">
                <button
                  onClick={scrollToProjects}
                  className="relative inline-block p-px font-semibold leading-6 text-white bg-neutral-900 shadow-2xl cursor-pointer rounded-2xl shadow-emerald-900 transition-all duration-300 ease-in-out hover:scale-105 active:scale-95 hover:shadow-emerald-600"
                >
                  <span className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500 via-cyan-500 to-sky-600 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                  <span className="relative z-10 block px-6 py-3 rounded-2xl bg-neutral-950">
                    <div className="relative z-10 flex items-center space-x-3">
                      <span className="transition-all duration-500 group-hover:translate-x-1.5 group-hover:text-emerald-300">
                        View Projects
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-7 h-7 transition-all duration-500 group-hover:translate-x-1.5 group-hover:text-emerald-300"
                      >
                        <path d="M16.172 11l-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"></path>
                      </svg>
                    </div>
                  </span>
                </button>
              </div>
              <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-black font-semibold leading-6 px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/30 hover:border-white/50">
                Download Résumé
              </button>
            </div>
          </div>

          {/* Right side - Image */}
          <div className="flex justify-center lg:justify-end order-1 lg:order-2">
            <div className="relative w-full max-w-md lg:max-w-lg">
              {/* Image container with glassmorphism effect */}
              <div className="relative overflow-hidden rounded-2xl glass border-2 border-white/20 shadow-2xl">
                <img
                  src="/image1.jpg"
                  alt="Jeetheshwar Aalam - AI/ML Engineer"
                  className="w-full h-auto object-cover transition-transform duration-300 hover:scale-105"
                  onError={(e) => {
                    // Fallback if image1 doesn't exist
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                {/* Fallback placeholder */}
                <div className="hidden w-full h-96 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 mx-auto mb-4 bg-white/10 rounded-full flex items-center justify-center">
                      <svg className="w-12 h-12 text-white/50" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-white/70 text-sm">Add image1.jpg to public folder</p>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-emerald-400/20 to-cyan-400/20 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 rounded-full blur-xl"></div>
            </div>
          </div>
        </div>

        <div className="absolute bottom-8 animate-bounce">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </main>

      {/* About Section */}
      <AboutSection />

      {/* Projects Section */}
      <ProjectsSection />

      {/* Data Visualization Section */}
      <DataVisualization />

      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Contact Section */}
      <ContactSection />
    </>
  )
}
